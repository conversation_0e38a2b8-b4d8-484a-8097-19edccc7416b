using System.Text;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Mvc;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密锁测试服务（用于调试和测试）
/// 版 本:V5.0.0
/// 版 权:杭州峰回科技有限公司
/// 作 者:系统生成
/// 日 期:2024-01-20
/// </summary>
[ApiDescriptionSettings("加密锁测试")]
public class DongleTestService : IDynamicApiController, ITransient
{
    private readonly DongleApiService _dongleApiService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dongleApiService">加密锁API服务</param>
    public DongleTestService(DongleApiService dongleApiService)
    {
        _dongleApiService = dongleApiService;
    }

    /// <summary>
    /// 测试加密锁基本功能（类似WinForm示例）
    /// </summary>
    /// <returns>测试结果</returns>
    [HttpPost("/dongleTest/basicTest")]
    public dynamic BasicTest()
    {
        var result = new StringBuilder();
        
        try
        {
            // 1. 第一次枚举加密锁：获取设备数量
            result.AppendLine("=== 加密锁基本功能测试 ===");

            var (enumResult, pDongleInfo, pCount) = _dongleApiService.EnumDongle();

            if (enumResult != 0)
            {
                result.AppendLine($"Enum Dongle Failed! Return value: {enumResult:X}");
                throw Oops.Oh($"枚举加密锁失败，错误代码: {enumResult:X}");
            }

            result.AppendLine($"Enum Dongle Success! Count: {pCount}");

            // 2. 第二次枚举加密锁：获取设备详细信息
            var (getInfoResult, dongleInfo, count) = _dongleApiService.EnumDongle();
            if (getInfoResult != 0)
            {
                result.AppendLine($"GetInfo Dongle Failed! Return value: {getInfoResult:X}");
                throw Oops.Oh($"获取设备信息失败，错误代码: {getInfoResult:X}");
            }

            result.AppendLine("GetInfo Dongle Success!");
            
            // 3. 显示设备详细信息（使用第一次枚举的count，第二次枚举的dongleInfo）
            for (int k = 0; k < pCount; k++)
            {
                result.AppendLine($"\n*********Dongle ARM INFO*******");
                result.AppendLine($"The index: {k}");
                result.AppendLine($"Agent ID: {dongleInfo.m_Agent:X}");
                result.AppendLine($"Dev Type: {dongleInfo.m_DevType}");
                result.Append("HID: ");

                if (dongleInfo.m_HID != null)
                {
                    for (int i = 0; i < 8; i++)
                    {
                        result.Append($"{dongleInfo.m_HID[i]:X2} ");
                    }
                }
                result.AppendLine();

                if (dongleInfo.m_BirthDay != null && dongleInfo.m_BirthDay.Length >= 6)
                {
                    result.AppendLine($"Birth day: 20{dongleInfo.m_BirthDay[0]:X2}-{dongleInfo.m_BirthDay[1]:X2}-{dongleInfo.m_BirthDay[2]:X2} {dongleInfo.m_BirthDay[3]:X2}:{dongleInfo.m_BirthDay[4]:X2}:{dongleInfo.m_BirthDay[5]:X2}");
                }

                result.AppendLine($"Is Mother Dongle: {dongleInfo.m_IsMother}");
                result.AppendLine($"PID: {dongleInfo.m_PID:X}");
                result.AppendLine($"Product Type: {dongleInfo.m_Type:X}");
                result.AppendLine($"UID: {dongleInfo.m_UserID:X}");
            }
            
            // 4. 测试打开和关闭设备
            var (openResult, hDongle) = _dongleApiService.OpenDongle(0);
            if (openResult != 0)
            {
                result.AppendLine($"Open Dongle Failed! Return value: {openResult:X}");
                throw Oops.Oh($"打开设备失败，错误代码: {openResult:X}");
            }
            
            result.AppendLine("Open Dongle Success!");
            
            // 5. 关闭设备
            var closeResult = _dongleApiService.CloseDongle(hDongle);
            if (closeResult != 0)
            {
                result.AppendLine($"Close Dongle Failed! Return value: {closeResult:X}");
                throw Oops.Oh($"关闭设备失败，错误代码: {closeResult:X}");
            }
            
            result.AppendLine("Close Dongle Success!");
            
            return new 
            { 
                message = "测试完成", 
                data = result.ToString(),
                deviceCount = pCount
            };
        }
        catch (Exception ex)
        {
            result.AppendLine($"Exception: {ex.Message}");
            throw Oops.Oh($"测试过程中发生异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试设备枚举
    /// </summary>
    /// <returns>枚举结果</returns>
    [HttpGet("/dongleTest/enumTest")]
    public dynamic EnumTest()
    {
        var (result, dongleInfo, count) = _dongleApiService.EnumDongle();
        
        if (result != 0)
        {
            throw Oops.Oh($"枚举失败，错误代码: {result:X}");
        }
        
        return new
        {
            count,
            deviceInfo = new
            {
                version = dongleInfo.m_Ver.ToString("X4"),
                type = dongleInfo.m_Type.ToString("X"),
                agentId = dongleInfo.m_Agent.ToString("X"),
                productId = dongleInfo.m_PID.ToString("X"),
                userId = dongleInfo.m_UserID.ToString("X"),
                isMother = dongleInfo.m_IsMother == 1,
                deviceType = dongleInfo.m_DevType,
                hardwareId = dongleInfo.m_HID != null ? 
                    string.Join(" ", dongleInfo.m_HID.Take(8).Select(b => b.ToString("X2"))) : "N/A",
                birthDay = dongleInfo.m_BirthDay != null && dongleInfo.m_BirthDay.Length >= 6 ?
                    $"20{dongleInfo.m_BirthDay[0]:X2}-{dongleInfo.m_BirthDay[1]:X2}-{dongleInfo.m_BirthDay[2]:X2} {dongleInfo.m_BirthDay[3]:X2}:{dongleInfo.m_BirthDay[4]:X2}:{dongleInfo.m_BirthDay[5]:X2}" : "N/A"
            }
        };
    }

    /// <summary>
    /// 测试设备打开关闭
    /// </summary>
    /// <param name="input">测试参数</param>
    /// <returns>测试结果</returns>
    [HttpPost("/dongleTest/openCloseTest")]
    public dynamic OpenCloseTest([FromQuery] DongleTestInput input)
    {
        // 严格按照WinForm示例的方式
        uint hDongle = 0;

        // 打开设备
        var (openResult, handle) = _dongleApiService.OpenDongle(input.Index);
        hDongle = handle;

        if (openResult != 0)
        {
            throw Oops.Oh($"Open Dongle Failed! Return value: {openResult:X}");
        }

        // 关闭设备
        var closeResult = _dongleApiService.CloseDongle(hDongle);
        if (closeResult != 0)
        {
            throw Oops.Oh($"Close Dongle Failed! Return value: {closeResult:X}");
        }

        return new
        {
            message = "设备打开关闭测试成功",
            openResult = openResult.ToString("X"),
            closeResult = closeResult.ToString("X"),
            handle = hDongle.ToString("X"),
            index = input.Index
        };
    }

    /// <summary>
    /// 详细的设备打开关闭调试测试
    /// </summary>
    /// <param name="input">测试参数</param>
    /// <returns>详细测试结果</returns>
    [HttpPost("/dongleTest/debugOpenCloseTest")]
    public dynamic DebugOpenCloseTest([FromQuery] DongleTestInput input)
    {
        var result = new System.Text.StringBuilder();
        uint hDongle = 0;

        try
        {
            result.AppendLine("=== 详细的设备打开关闭调试测试 ===");
            result.AppendLine($"测试设备索引: {input.Index}");

            // 先枚举设备
            var (enumResult, dongleInfo, count) = _dongleApiService.EnumDongle();
            result.AppendLine($"枚举结果: {enumResult:X}, 设备数量: {count}");

            if (enumResult != 0)
            {
                throw Oops.Oh($"枚举设备失败: {enumResult:X}");
            }

            if (count == 0)
            {
                throw Oops.Oh("未发现设备");
            }

            // 打开设备
            result.AppendLine($"尝试打开设备索引 {input.Index}...");
            var (openResult, handle) = _dongleApiService.OpenDongle(input.Index);
            hDongle = handle;

            result.AppendLine($"打开结果: {openResult:X}");
            result.AppendLine($"设备句柄: {hDongle:X}");

            if (openResult != 0)
            {
                throw Oops.Oh($"Open Dongle Failed! Return value: {openResult:X}");
            }

            result.AppendLine("设备打开成功!");

            // 关闭设备
            result.AppendLine($"尝试关闭设备，句柄: {hDongle:X}...");
            var closeResult = _dongleApiService.CloseDongle(hDongle);

            result.AppendLine($"关闭结果: {closeResult:X}");

            if (closeResult != 0)
            {
                throw Oops.Oh($"Close Dongle Failed! Return value: {closeResult:X}");
            }

            result.AppendLine("设备关闭成功!");

            return new
            {
                success = true,
                message = "详细测试完成",
                details = result.ToString(),
                openResult = openResult.ToString("X"),
                closeResult = closeResult.ToString("X"),
                handle = hDongle.ToString("X")
            };
        }
        catch (Exception ex)
        {
            result.AppendLine($"测试过程中发生异常: {ex.Message}");

            // 如果设备已打开，尝试关闭
            if (hDongle != 0)
            {
                try
                {
                    result.AppendLine($"尝试强制关闭设备，句柄: {hDongle:X}...");
                    var forceCloseResult = _dongleApiService.CloseDongle(hDongle);
                    result.AppendLine($"强制关闭结果: {forceCloseResult:X}");
                }
                catch (Exception forceEx)
                {
                    result.AppendLine($"强制关闭也失败了: {forceEx.Message}");
                }
            }

            throw Oops.Oh($"测试失败: {ex.Message}\n详细信息:\n{result}");
        }
    }
}

/// <summary>
/// 加密锁测试输入参数
/// </summary>
public class DongleTestInput
{
    /// <summary>
    /// 设备索引
    /// </summary>
    public int Index { get; set; } = 0;
}
