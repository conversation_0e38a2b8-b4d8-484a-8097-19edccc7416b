using DongleTestConsole;
using System.Text;

Console.OutputEncoding = Encoding.UTF8;
Console.WriteLine("=== 加密锁测试控制台程序 ===");
Console.WriteLine($"程序启动时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
Console.WriteLine();

var tester = new DongleTester();

while (true)
{
    Console.WriteLine("请选择测试类型:");
    Console.WriteLine("1. 基本功能测试 (复制WinForm逻辑)");
    Console.WriteLine("2. 压力测试 (多轮打开关闭)");
    Console.WriteLine("3. 自定义压力测试");
    Console.WriteLine("4. 详细诊断测试 (环境检查+多次尝试)");
    Console.WriteLine("5. 退出程序");
    Console.Write("请输入选择 (1-5): ");

    var choice = Console.ReadLine();

    try
    {
        switch (choice)
        {
            case "1":
                Console.WriteLine();
                Console.WriteLine("开始执行基本功能测试...");
                Console.WriteLine();
                
                var basicResult = await tester.RunBasicTestAsync();
                Console.WriteLine(basicResult);
                break;

            case "2":
                Console.WriteLine();
                Console.WriteLine("开始执行压力测试 (10轮)...");
                Console.WriteLine();
                
                var stressResult = await tester.RunStressTestAsync(10);
                Console.WriteLine(stressResult);
                break;

            case "3":
                Console.WriteLine();
                Console.Write("请输入测试轮数 (1-1000): ");
                var iterationsInput = Console.ReadLine();
                
                if (int.TryParse(iterationsInput, out int iterations) && iterations >= 1 && iterations <= 1000)
                {
                    Console.WriteLine();
                    Console.WriteLine($"开始执行自定义压力测试 ({iterations}轮)...");
                    Console.WriteLine();
                    
                    var customStressResult = await tester.RunStressTestAsync(iterations);
                    Console.WriteLine(customStressResult);
                }
                else
                {
                    Console.WriteLine("❌ 无效的轮数，请输入1-1000之间的数字");
                }
                break;

            case "4":
                Console.WriteLine();
                Console.WriteLine("开始执行详细诊断测试...");
                Console.WriteLine();
                
                var diagnosticResult = await tester.RunDiagnosticTestAsync();
                Console.WriteLine(diagnosticResult);
                break;

            case "5":
                Console.WriteLine("程序退出中...");
                return;

            default:
                Console.WriteLine("❌ 无效的选择，请重新输入");
                break;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ 执行测试时发生异常:");
        Console.WriteLine($"   异常类型: {ex.GetType().Name}");
        Console.WriteLine($"   异常消息: {ex.Message}");
        Console.WriteLine($"   异常堆栈: {ex.StackTrace}");
    }

    Console.WriteLine();
    Console.WriteLine("按任意键继续...");
    Console.ReadKey();
    Console.Clear();
    
    Console.WriteLine("=== 加密锁测试控制台程序 ===");
    Console.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
    Console.WriteLine();
}
