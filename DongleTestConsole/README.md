# 加密锁测试控制台程序

这是一个专门用于测试加密锁功能的控制台应用程序，特别针对打开和关闭操作的问题进行诊断。

## 功能特性

- **基本功能测试**: 复制WinForm示例的完整逻辑
- **压力测试**: 多轮打开关闭操作测试
- **详细错误报告**: 提供详细的错误代码和描述
- **句柄管理**: 自动跟踪和清理未关闭的句柄
- **性能监控**: 记录操作耗时和成功率

## 使用方法

### 1. 准备DLL文件

首先需要将 `Dongle_d.dll` 文件复制到程序目录：

```bash
# 运行批处理文件自动复制
copy_dll.bat

# 或手动复制DLL文件到当前目录
```

### 2. 编译和运行

**方法一：使用批处理文件（推荐）**
```bash
# 运行自动化脚本（包含环境检查、编译、运行）
build_and_run.bat
```

**方法二：使用简单运行脚本**
```bash
# 运行测试脚本（已编译完成）
run_test.bat
```

**方法三：手动编译运行**
```bash
# 编译项目
dotnet build

# 运行程序
dotnet run
```

### 3. 测试选项

程序提供以下测试选项：

1. **基本功能测试**: 
   - 枚举加密锁设备
   - 显示设备详细信息
   - 测试每个设备的打开关闭操作
   - 自动清理未关闭的句柄

2. **压力测试 (10轮)**:
   - 连续执行10轮打开关闭操作
   - 统计成功率和平均耗时
   - 检测潜在的资源泄漏问题

3. **自定义压力测试**:
   - 可指定测试轮数 (1-1000)
   - 适用于长时间稳定性测试

4. **详细诊断测试**:
   - 环境检查 (操作系统、.NET版本、DLL文件)
   - 多次枚举测试 (检测枚举稳定性)
   - 详细的打开关闭测试 (多次尝试、重试机制)
   - 完整的错误诊断和性能分析

## 输出示例

### 基本功能测试输出
```
=== 加密锁基本功能测试 ===
测试开始时间: 2024-01-20 10:30:15.123

步骤1: 枚举加密锁设备...
✅ Enum Dongle Success! Count: 1

步骤2: 获取设备详细信息...
✅ GetInfo Dongle Success!

********** Dongle ARM INFO (Index: 0) **********
Agent ID: 0xFFFFFFFF
Dev Type: 1
HID: 12 34 56 78 9A BC DE F0 
Birth day: 2024-01-15 10:30:25
Is Mother Dongle: 0
PID: 0x12345678
Product Type: 0xFF
UID: 0x87654321

步骤3: 测试打开和关闭操作...
  测试设备 0:
    ✅ Open Dongle Success! Handle: 0x12345678
    ✅ Close Dongle Success! Handle: 0x12345678

测试结束时间: 2024-01-20 10:30:16.456
总耗时: 1333 ms
```

### 压力测试输出
```
=== 加密锁压力测试 ===
测试轮数: 10
测试开始时间: 2024-01-20 10:35:00.000

第 1 轮测试:
  ✅ 成功 (耗时: 125 ms)
第 2 轮测试:
  ✅ 成功 (耗时: 118 ms)
...

=== 压力测试结果 ===
总轮数: 10
成功: 10
失败: 0
成功率: 100.00%
总耗时: 2500 ms
平均耗时: 250 ms/轮
```

## 错误诊断

程序会提供详细的错误信息：

- **错误代码**: 以十六进制格式显示
- **错误描述**: 人类可读的错误说明
- **句柄跟踪**: 显示哪些句柄未能正确关闭
- **异常信息**: 完整的异常堆栈跟踪

## 常见问题

### 1. DLL文件未找到
```
错误: 无法加载DLL 'Dongle_d.dll'
解决: 确保Dongle_d.dll文件在程序目录中
```

### 2. 设备未检测到
```
⚠️ 未检测到加密锁设备
解决: 检查设备连接和驱动程序
```

### 3. 关闭操作失败
```
❌ Close Dongle Failed! Return value: 0x00000005
错误描述: 设备未打开
解决: 检查句柄是否有效，可能设备已断开
```

## 技术细节

- **平台**: .NET 8.0
- **架构**: x64 (与DLL匹配)
- **P/Invoke**: 使用原生DLL调用
- **内存管理**: 自动跟踪和清理资源
- **异步操作**: 支持异步测试避免界面阻塞

## 注意事项

1. 确保使用x64平台编译，与DLL架构匹配
2. 程序会自动清理未关闭的句柄，但建议正常退出
3. 压力测试时注意设备的物理连接稳定性
4. 如果发现关闭失败，可以尝试重新插拔设备

## 故障排除

如果遇到关闭失败的问题：

1. 运行基本功能测试，查看详细错误信息
2. 运行压力测试，检查是否存在模式性问题
3. 检查设备管理器中的设备状态
4. 尝试重新安装设备驱动程序
5. 联系设备厂商获取技术支持
