using System.Diagnostics;
using System.Text;
using static DongleTestConsole.DongleApi;
using static DongleTestConsole.DongleStructures;

namespace DongleTestConsole;

/// <summary>
/// 加密锁测试器
/// </summary>
public class DongleTester
{
    private readonly List<uint> _openHandles = new();

    /// <summary>
    /// 执行基本功能测试（复制WinForm逻辑）
    /// </summary>
    /// <returns>测试结果</returns>
    public async Task<string> RunBasicTestAsync()
    {
        var result = new StringBuilder();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            result.AppendLine("=== 加密锁基本功能测试 ===");
            result.AppendLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            result.AppendLine();

            // 1. 第一次枚举加密锁：获取设备数量
            result.AppendLine("步骤1: 枚举加密锁设备...");
            var pDongleInfo = new DONGLE_INFO();
            ushort pCount = 0;
            uint ret = Dongle_Enum(ref pDongleInfo, out pCount);

            if (ret != 0)
            {
                result.AppendLine($"❌ Enum Dongle Failed! Return value: 0x{ret:X8}");
                result.AppendLine($"   错误描述: {GetErrorDescription(ret)}");
                return result.ToString();
            }

            result.AppendLine($"✅ Enum Dongle Success! Count: {pCount}");

            if (pCount == 0)
            {
                result.AppendLine("⚠️  未检测到加密锁设备");
                return result.ToString();
            }

            // 2. 第二次枚举加密锁：获取设备详细信息
            result.AppendLine();
            result.AppendLine("步骤2: 获取设备详细信息...");
            ret = Dongle_Enum(ref pDongleInfo, out pCount);
            if (ret != 0)
            {
                result.AppendLine($"❌ GetInfo Dongle Failed! Return value: 0x{ret:X8}");
                result.AppendLine($"   错误描述: {GetErrorDescription(ret)}");
                return result.ToString();
            }

            result.AppendLine("✅ GetInfo Dongle Success!");

            // 3. 显示设备信息（按照WinForm的格式）
            result.AppendLine();
            for (int k = 0; k < pCount; k++)
            {
                result.AppendLine($"********** Dongle ARM INFO (Index: {k}) **********");
                result.AppendLine($"Agent ID: 0x{pDongleInfo.m_Agent:X}");
                result.AppendLine($"Dev Type: {pDongleInfo.m_DevType}");
                
                result.Append("HID: ");
                for (int i = 0; i < 8; i++)
                {
                    result.Append($"{pDongleInfo.m_HID[i]:X2} ");
                }
                result.AppendLine();

                result.AppendLine($"Birth day: 20{pDongleInfo.m_BirthDay[0]:X2}-{pDongleInfo.m_BirthDay[1]:X2}-{pDongleInfo.m_BirthDay[2]:X2} " +
                                $"{pDongleInfo.m_BirthDay[3]:X2}:{pDongleInfo.m_BirthDay[4]:X2}:{pDongleInfo.m_BirthDay[5]:X2}");
                result.AppendLine($"Is Mother Dongle: {pDongleInfo.m_IsMother}");
                result.AppendLine($"PID: 0x{pDongleInfo.m_PID:X}");
                result.AppendLine($"Product Type: 0x{pDongleInfo.m_Type:X}");
                result.AppendLine($"UID: 0x{pDongleInfo.m_UserID:X}");
                result.AppendLine();
            }

            // 4. 测试打开和关闭操作
            result.AppendLine("步骤3: 测试打开和关闭操作...");
            await TestOpenCloseOperationsAsync(result, pCount);

        }
        catch (Exception ex)
        {
            result.AppendLine($"❌ 测试过程中发生异常: {ex.Message}");
            result.AppendLine($"   异常堆栈: {ex.StackTrace}");
        }
        finally
        {
            // 确保清理所有打开的句柄
            await CleanupHandlesAsync(result);
            
            stopwatch.Stop();
            result.AppendLine();
            result.AppendLine($"测试结束时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            result.AppendLine($"总耗时: {stopwatch.ElapsedMilliseconds} ms");
        }

        return result.ToString();
    }

    /// <summary>
    /// 测试打开和关闭操作
    /// </summary>
    private async Task TestOpenCloseOperationsAsync(StringBuilder result, int deviceCount)
    {
        for (int index = 0; index < deviceCount; index++)
        {
            result.AppendLine($"  测试设备 {index}:");
            
            // 测试打开操作
            uint hDongle = 0;
            uint openResult = Dongle_Open(ref hDongle, index);
            
            if (openResult != 0)
            {
                result.AppendLine($"    ❌ Open Dongle Failed! Return value: 0x{openResult:X8}");
                result.AppendLine($"       错误描述: {GetErrorDescription(openResult)}");
                continue;
            }

            result.AppendLine($"    ✅ Open Dongle Success! Handle: 0x{hDongle:X8}");
            _openHandles.Add(hDongle);

            // 添加短暂延迟，模拟实际使用场景
            await Task.Delay(100);

            // 测试关闭操作
            uint closeResult = Dongle_Close(hDongle);
            
            if (closeResult != 0)
            {
                result.AppendLine($"    ❌ Close Dongle Failed! Return value: 0x{closeResult:X8}");
                result.AppendLine($"       错误描述: {GetErrorDescription(closeResult)}");
                result.AppendLine($"       句柄 0x{hDongle:X8} 可能未正确关闭");
            }
            else
            {
                result.AppendLine($"    ✅ Close Dongle Success! Handle: 0x{hDongle:X8}");
                _openHandles.Remove(hDongle);
            }

            result.AppendLine();
        }
    }

    /// <summary>
    /// 清理所有打开的句柄
    /// </summary>
    private async Task CleanupHandlesAsync(StringBuilder result)
    {
        if (_openHandles.Count > 0)
        {
            result.AppendLine("步骤4: 清理未关闭的句柄...");
            
            var handlesCopy = new List<uint>(_openHandles);
            foreach (var handle in handlesCopy)
            {
                try
                {
                    result.AppendLine($"  尝试关闭句柄: 0x{handle:X8}");
                    uint closeResult = Dongle_Close(handle);
                    
                    if (closeResult == 0)
                    {
                        result.AppendLine($"    ✅ 句柄 0x{handle:X8} 关闭成功");
                        _openHandles.Remove(handle);
                    }
                    else
                    {
                        result.AppendLine($"    ❌ 句柄 0x{handle:X8} 关闭失败: 0x{closeResult:X8}");
                        result.AppendLine($"       错误描述: {GetErrorDescription(closeResult)}");
                    }
                }
                catch (Exception ex)
                {
                    result.AppendLine($"    ❌ 关闭句柄 0x{handle:X8} 时发生异常: {ex.Message}");
                }

                // 添加延迟，避免操作过快
                await Task.Delay(50);
            }

            if (_openHandles.Count > 0)
            {
                result.AppendLine($"⚠️  仍有 {_openHandles.Count} 个句柄未能正确关闭");
            }
            else
            {
                result.AppendLine("✅ 所有句柄已正确关闭");
            }
        }
    }

    /// <summary>
    /// 执行压力测试
    /// </summary>
    public async Task<string> RunStressTestAsync(int iterations = 10)
    {
        var result = new StringBuilder();
        var stopwatch = Stopwatch.StartNew();

        result.AppendLine("=== 加密锁压力测试 ===");
        result.AppendLine($"测试轮数: {iterations}");
        result.AppendLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
        result.AppendLine();

        int successCount = 0;
        int failCount = 0;

        for (int i = 1; i <= iterations; i++)
        {
            result.AppendLine($"第 {i} 轮测试:");
            
            try
            {
                var testResult = await RunSingleOpenCloseTestAsync();
                if (testResult.Success)
                {
                    successCount++;
                    result.AppendLine($"  ✅ 成功 (耗时: {testResult.ElapsedMs} ms)");
                }
                else
                {
                    failCount++;
                    result.AppendLine($"  ❌ 失败: {testResult.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                failCount++;
                result.AppendLine($"  ❌ 异常: {ex.Message}");
            }

            // 轮次间延迟
            await Task.Delay(200);
        }

        stopwatch.Stop();
        
        result.AppendLine();
        result.AppendLine("=== 压力测试结果 ===");
        result.AppendLine($"总轮数: {iterations}");
        result.AppendLine($"成功: {successCount}");
        result.AppendLine($"失败: {failCount}");
        result.AppendLine($"成功率: {(double)successCount / iterations * 100:F2}%");
        result.AppendLine($"总耗时: {stopwatch.ElapsedMilliseconds} ms");
        result.AppendLine($"平均耗时: {stopwatch.ElapsedMilliseconds / iterations} ms/轮");

        return result.ToString();
    }

    /// <summary>
    /// 执行单次打开关闭测试
    /// </summary>
    private async Task<(bool Success, string ErrorMessage, long ElapsedMs)> RunSingleOpenCloseTestAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // 枚举设备
            var pDongleInfo = new DONGLE_INFO();
            ushort pCount = 0;
            uint enumResult = Dongle_Enum(ref pDongleInfo, out pCount);
            
            if (enumResult != 0)
            {
                return (false, $"枚举失败: 0x{enumResult:X8}", stopwatch.ElapsedMilliseconds);
            }

            if (pCount == 0)
            {
                return (false, "未检测到设备", stopwatch.ElapsedMilliseconds);
            }

            // 打开设备
            uint hDongle = 0;
            uint openResult = Dongle_Open(ref hDongle, 0);
            
            if (openResult != 0)
            {
                return (false, $"打开失败: 0x{openResult:X8}", stopwatch.ElapsedMilliseconds);
            }

            // 短暂延迟
            await Task.Delay(10);

            // 关闭设备
            uint closeResult = Dongle_Close(hDongle);
            
            if (closeResult != 0)
            {
                return (false, $"关闭失败: 0x{closeResult:X8}", stopwatch.ElapsedMilliseconds);
            }

            return (true, string.Empty, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            return (false, $"异常: {ex.Message}", stopwatch.ElapsedMilliseconds);
        }
    }

    /// <summary>
    /// 执行详细诊断测试
    /// </summary>
    public async Task<string> RunDiagnosticTestAsync()
    {
        var result = new StringBuilder();
        var stopwatch = Stopwatch.StartNew();

        result.AppendLine("=== 加密锁详细诊断测试 ===");
        result.AppendLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
        result.AppendLine();

        try
        {
            // 1. 环境检查
            result.AppendLine("步骤1: 环境检查");
            result.AppendLine($"  操作系统: {Environment.OSVersion}");
            result.AppendLine($"  .NET版本: {Environment.Version}");
            result.AppendLine($"  进程架构: {(Environment.Is64BitProcess ? "64" : "32")}位");
            result.AppendLine($"  工作目录: {Environment.CurrentDirectory}");

            // 检查DLL文件
            var dllPath = Path.Combine(Environment.CurrentDirectory, "Dongle_d.dll");
            if (File.Exists(dllPath))
            {
                var fileInfo = new FileInfo(dllPath);
                result.AppendLine($"  ✅ DLL文件存在: {dllPath}");
                result.AppendLine($"     文件大小: {fileInfo.Length} 字节");
                result.AppendLine($"     修改时间: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
            }
            else
            {
                result.AppendLine($"  ❌ DLL文件不存在: {dllPath}");
                return result.ToString();
            }
            result.AppendLine();

            // 2. 多次枚举测试
            result.AppendLine("步骤2: 多次枚举测试");
            for (int i = 1; i <= 5; i++)
            {
                var enumStopwatch = Stopwatch.StartNew();
                var pDongleInfo = new DONGLE_INFO();
                ushort pCount = 0;
                uint enumResult = Dongle_Enum(ref pDongleInfo, out pCount);
                enumStopwatch.Stop();

                result.AppendLine($"  第{i}次枚举: ");
                if (enumResult == 0)
                {
                    result.AppendLine($"    ✅ 成功，设备数量: {pCount}，耗时: {enumStopwatch.ElapsedMilliseconds}ms");
                }
                else
                {
                    result.AppendLine($"    ❌ 失败，错误代码: 0x{enumResult:X8}，耗时: {enumStopwatch.ElapsedMilliseconds}ms");
                    result.AppendLine($"       错误描述: {GetErrorDescription(enumResult)}");
                }

                await Task.Delay(100);
            }
            result.AppendLine();

            // 3. 详细的打开关闭测试
            result.AppendLine("步骤3: 详细的打开关闭测试");
            await RunDetailedOpenCloseTestAsync(result);

        }
        catch (Exception ex)
        {
            result.AppendLine($"❌ 诊断测试过程中发生异常: {ex.Message}");
            result.AppendLine($"   异常堆栈: {ex.StackTrace}");
        }
        finally
        {
            stopwatch.Stop();
            result.AppendLine();
            result.AppendLine($"诊断测试结束时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            result.AppendLine($"总耗时: {stopwatch.ElapsedMilliseconds} ms");
        }

        return result.ToString();
    }

    /// <summary>
    /// 执行详细的打开关闭测试
    /// </summary>
    private async Task RunDetailedOpenCloseTestAsync(StringBuilder result)
    {
        // 先枚举获取设备信息
        var pDongleInfo = new DONGLE_INFO();
        ushort pCount = 0;
        uint enumResult = Dongle_Enum(ref pDongleInfo, out pCount);

        if (enumResult != 0 || pCount == 0)
        {
            result.AppendLine("  ❌ 无法获取设备信息，跳过打开关闭测试");
            return;
        }

        for (int deviceIndex = 0; deviceIndex < pCount; deviceIndex++)
        {
            result.AppendLine($"  测试设备 {deviceIndex}:");

            // 多次打开关闭测试
            for (int attempt = 1; attempt <= 3; attempt++)
            {
                result.AppendLine($"    第{attempt}次尝试:");

                var openStopwatch = Stopwatch.StartNew();
                uint hDongle = 0;
                uint openResult = Dongle_Open(ref hDongle, deviceIndex);
                openStopwatch.Stop();

                if (openResult != 0)
                {
                    result.AppendLine($"      ❌ 打开失败: 0x{openResult:X8} ({GetErrorDescription(openResult)})");
                    result.AppendLine($"         打开耗时: {openStopwatch.ElapsedMilliseconds}ms");
                    continue;
                }

                result.AppendLine($"      ✅ 打开成功: 句柄=0x{hDongle:X8}，耗时: {openStopwatch.ElapsedMilliseconds}ms");

                // 等待一段时间
                await Task.Delay(50);

                // 尝试关闭
                var closeStopwatch = Stopwatch.StartNew();
                uint closeResult = Dongle_Close(hDongle);
                closeStopwatch.Stop();

                if (closeResult != 0)
                {
                    result.AppendLine($"      ❌ 关闭失败: 0x{closeResult:X8} ({GetErrorDescription(closeResult)})");
                    result.AppendLine($"         关闭耗时: {closeStopwatch.ElapsedMilliseconds}ms");

                    // 尝试强制关闭
                    result.AppendLine($"      🔄 尝试再次关闭...");
                    await Task.Delay(100);
                    uint retryCloseResult = Dongle_Close(hDongle);
                    if (retryCloseResult == 0)
                    {
                        result.AppendLine($"      ✅ 重试关闭成功");
                    }
                    else
                    {
                        result.AppendLine($"      ❌ 重试关闭仍失败: 0x{retryCloseResult:X8}");
                    }
                }
                else
                {
                    result.AppendLine($"      ✅ 关闭成功，耗时: {closeStopwatch.ElapsedMilliseconds}ms");
                }

                // 尝试间隔
                await Task.Delay(200);
            }
            result.AppendLine();
        }
    }
}
