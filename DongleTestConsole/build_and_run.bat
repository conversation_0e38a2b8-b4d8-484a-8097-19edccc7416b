@echo off
echo ========================================
echo 加密锁测试控制台程序 - 编译和运行脚本
echo ========================================
echo.

REM 设置控制台编码为UTF-8
chcp 65001 > nul

echo 步骤1: 检查.NET环境...
dotnet --version
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到.NET SDK，请先安装.NET 8.0 SDK
    pause
    exit /b 1
)
echo ✅ .NET环境检查通过
echo.

echo 步骤2: 检查DLL文件...
if exist "Dongle_d.dll" (
    echo ✅ 找到Dongle_d.dll文件
) else (
    echo ⚠️  未找到Dongle_d.dll文件，尝试自动复制...
    call copy_dll.bat
    if not exist "Dongle_d.dll" (
        echo ❌ 无法找到Dongle_d.dll文件
        echo 请手动将Dongle_d.dll文件复制到当前目录
        pause
        exit /b 1
    )
)
echo.

echo 步骤3: 清理之前的编译结果...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
echo ✅ 清理完成
echo.

echo 步骤4: 编译项目...
dotnet build --configuration Release --verbosity minimal
if %ERRORLEVEL% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译成功
echo.

echo 步骤5: 运行程序...
echo ========================================
echo.
dotnet run --configuration Release

echo.
echo ========================================
echo 程序执行完毕
pause
