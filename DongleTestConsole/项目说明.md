# 加密锁测试控制台程序 - 项目说明

## 项目背景

您在使用WinForm程序测试加密锁时遇到了关闭操作失败的问题。为了更好地诊断和解决这个问题，我们创建了这个专门的控制台测试程序。

## 解决的问题

1. **关闭操作失败诊断**: 提供详细的错误信息和诊断功能
2. **压力测试**: 通过多轮测试检测问题的模式性
3. **环境检查**: 验证运行环境和DLL文件状态
4. **句柄管理**: 自动跟踪和清理未关闭的句柄

## 项目结构

```
DongleTestConsole/
├── DongleTestConsole.csproj    # 项目文件
├── Program.cs                  # 主程序入口
├── DongleTester.cs            # 测试器核心类
├── DongleApi.cs               # API接口定义
├── DongleStructures.cs        # 数据结构定义
├── Dongle_d.dll              # 加密锁动态库
├── build_and_run.bat         # 自动化编译运行脚本
├── run_test.bat              # 简单运行脚本
├── copy_dll.bat              # DLL复制脚本
├── README.md                 # 详细使用说明
└── 项目说明.md               # 本文件
```

## 核心功能

### 1. 基本功能测试
- 完全复制您的WinForm代码逻辑
- 枚举设备 → 获取信息 → 打开设备 → 关闭设备
- 详细的错误报告和性能监控

### 2. 压力测试
- 连续多轮的打开关闭操作
- 统计成功率和平均耗时
- 检测资源泄漏和稳定性问题

### 3. 详细诊断测试
- 环境检查（操作系统、.NET版本、DLL文件）
- 多次枚举测试（检测枚举稳定性）
- 详细的打开关闭测试（多次尝试、重试机制）
- 完整的错误诊断和性能分析

### 4. 智能句柄管理
- 自动跟踪所有打开的句柄
- 程序结束时自动清理未关闭的句柄
- 提供重试机制处理关闭失败

## 技术特点

1. **完全兼容**: 使用与您WinForm程序相同的结构体和API声明
2. **详细日志**: 提供比WinForm更详细的错误信息和诊断数据
3. **异步支持**: 避免界面阻塞，支持长时间测试
4. **错误恢复**: 智能的错误处理和恢复机制
5. **性能监控**: 精确的时间测量和性能分析

## 快速开始

### 方法一：一键运行（推荐）
```bash
# 进入项目目录
cd DongleTestConsole

# 运行自动化脚本
build_and_run.bat
```

### 方法二：简单运行
```bash
# 项目已编译，直接运行
run_test.bat
```

### 方法三：手动操作
```bash
# 确保DLL文件存在
# 编译项目
dotnet build

# 运行程序
dotnet run
```

## 测试建议

1. **首次使用**: 先运行"基本功能测试"，查看设备是否正常识别
2. **问题诊断**: 运行"详细诊断测试"，获取完整的环境和错误信息
3. **稳定性测试**: 运行"压力测试"，检查是否存在模式性问题
4. **长期测试**: 使用"自定义压力测试"进行长时间稳定性验证

## 预期结果

通过这个测试程序，您应该能够：

1. **确认问题**: 验证关闭操作失败是否可重现
2. **获取详情**: 得到详细的错误代码和描述信息
3. **分析原因**: 通过诊断测试了解问题的根本原因
4. **验证修复**: 在问题修复后验证解决方案的有效性

## 常见问题排查

### 如果程序无法启动
1. 检查是否安装了.NET 8.0 SDK
2. 确认Dongle_d.dll文件是否存在
3. 验证DLL文件架构是否匹配（x64）

### 如果设备无法检测
1. 检查设备物理连接
2. 验证设备驱动程序
3. 确认设备在设备管理器中的状态

### 如果关闭操作失败
1. 记录详细的错误代码
2. 检查是否存在多个程序同时访问设备
3. 尝试重新插拔设备
4. 联系设备厂商技术支持

## 后续步骤

1. **运行测试**: 使用本程序进行全面测试
2. **收集数据**: 记录所有错误信息和测试结果
3. **分析问题**: 基于测试结果分析问题原因
4. **寻求支持**: 如需要，可将测试结果提供给设备厂商

## 技术支持

如果在使用过程中遇到问题：

1. 查看README.md中的详细说明
2. 运行诊断测试获取详细信息
3. 检查程序输出的错误代码和描述
4. 根据错误信息进行相应的故障排除

这个测试程序应该能够帮助您更好地理解和解决加密锁关闭操作的问题。
