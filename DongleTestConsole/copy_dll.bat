@echo off
echo 正在复制加密锁DLL文件...

REM 从项目的其他位置复制DLL文件到当前目录
REM 请根据实际情况修改源路径

REM 尝试从可能的位置复制DLL
if exist "..\02-应用模块\11-Extend\IotPlatform.DongleLicense\Dongle_d.dll" (
    copy "..\02-应用模块\11-Extend\IotPlatform.DongleLicense\Dongle_d.dll" "Dongle_d.dll"
    echo 从IotPlatform.DongleLicense目录复制成功
    goto :end
)

if exist "..\..\02-应用模块\11-Extend\IotPlatform.DongleLicense\Dongle_d.dll" (
    copy "..\..\02-应用模块\11-Extend\IotPlatform.DongleLicense\Dongle_d.dll" "Dongle_d.dll"
    echo 从IotPlatform.DongleLicense目录复制成功
    goto :end
)

if exist "..\Dongle_d.dll" (
    copy "..\Dongle_d.dll" "Dongle_d.dll"
    echo 从上级目录复制成功
    goto :end
)

if exist "Dongle_d.dll" (
    echo DLL文件已存在
    goto :end
)

echo 警告: 未找到Dongle_d.dll文件
echo 请手动将Dongle_d.dll文件复制到当前目录
echo 或修改此批处理文件中的路径

:end
pause
